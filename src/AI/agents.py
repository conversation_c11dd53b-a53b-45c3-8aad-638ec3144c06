import logging

from langchain_core.output_parsers import <PERSON>son<PERSON>utputParser
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langchain_openai import ChatOpenAI
from langchain_tavily import TavilySearch

from models.IA.entry_analysis import EntryAnalysis
from models.IA.news_sentiment import NewsSentiment
from models.IA.trend_analysis import TrendAnalysis
from services.binance_service import BinanceService
from utils.logger_setup import setup_logger


class TrendAgent:
    """Un agente especialista que determina la tendencia principal del mercado."""

    def __init__(self):
        self.parser = JsonOutputParser(pydantic_object=TrendAnalysis)
        self.prompt = self._create_prompt_template()
        self.llm = ChatOpenAI(
            temperature=0, model="gpt-4-turbo"
        )  # Descomentar para uso real
        self.chain = self.prompt | self.llm | self.parser
        self.logger = setup_logger()

    def _create_prompt_template(self):
        return ChatPromptTemplate.from_template(
            """
            You are a Trend Analysis specialist. Your sole job is to determine the primary market trend for {trading_pair}.
            
            Format Instructions: {format_instructions}

            Market Data (4-hour chart):
            - Trading Pair: {trading_pair}
            - Current Price: {current_price}
            - EMA (200-period): {ema}

            Analysis Rules:
            - If Current Price > EMA 200, the trend is UPTREND.
            - If Current Price < EMA 200, the trend is DOWNTREND.
            - If the price is very close to the EMA, consider it SIDEWAYS.
            
            Provide your analysis.
            """,
            partial_variables={
                "format_instructions": self.parser.get_format_instructions()
            },
        )

    def invoke(self, market_data: dict) -> dict:
        self.logger.info("-> TrendAgent invoked.")

        price = market_data["price_indicators"]["current_price"]
        ema = market_data["overlap_studies"]["ema"]

        result = self.chain.invoke(
            {"trading_pair": "ETHUSDT", "current_price": price, "ema": ema}
        )
        logging.info(f"-> TrendAgent result: {result}")
        return result


class EntrySignalAgent:
    """Un agente especialista que busca señales de compra de alta probabilidad."""

    def __init__(self):
        self.parser = JsonOutputParser(pydantic_object=EntryAnalysis)
        self.prompt = self._create_prompt_template()
        self.llm = ChatOpenAI(temperature=0, model="gpt-4-turbo")
        self.chain = self.prompt | self.llm | self.parser
        self.logger = setup_logger()

    def _create_prompt_template(self):
        return ChatPromptTemplate.from_template(
            """
            You are an Entry Signal specialist. The primary trend is an UPTREND. 
            Your job is to find a high-probability "buy the dip" opportunity by analyzing BOTH the current market data AND the history of the last few periods.

            **Format Instructions:**
            {format_instructions}

            **Current Market Snapshot (JSON):**
            ```json
            {current_market_data}
            ```

            **Historical Context (Analysis from previous candles):**
            ```json
            {analysis_history}
            ```

            **Analysis Rules & Data Location:**
            1.  **Analyze the Present:** First, evaluate the `current_market_data` for a buy signal (e.g., price near `overlap_studies.bbands.lower` and low `momentum_indicators.rsi`).
            2.  **Analyze the Past (CRITICAL):** Now, compare the current situation with the `analysis_history`.
                - Is the RSI *lower* than in previous periods? (Indicates weakening selling pressure).
                - Is the price testing the lower Bollinger Band for the second or third time? (Indicates strong support).
                - Is the ATR (`volatility_indicators.atr`) decreasing? (Indicates the market is stabilizing for a potential entry).
            3.  **Synthesize:** Your final decision must be based on this comparison. A signal is much stronger if the historical context supports it. For example, a low RSI is more significant if it has been consistently dropping over the last few periods.
        
            Provide your analysis now.
            """,
            partial_variables={
                "format_instructions": self.parser.get_format_instructions()
            },
        )

    def invoke(self, agent_input: dict) -> dict:
        self.logger.info("-> EntrySignalAgent invoked.")

        return self.chain.invoke(
            {
                "current_market_data": agent_input["current_market_data"],
                "analysis_history": agent_input["analysis_history"],
            }
        )


class RiskManagerAgent:
    """Un agente especialista puramente matemático que calcula los parámetros de la operación."""

    def invoke(self, data: dict) -> dict:
        logging.info("-> RiskManagerAgent invoked.")
        try:
            current_price = data["market_data"]["price_indicators"]["current_price"]
            atr = data["market_data"]["volatility_indicators"]["atr"]

            stop_loss_distance = (
                atr * data["bot_config"]["atr_multiplier_for_stop_loss"]
            )
            stop_loss_price = current_price - stop_loss_distance

            risk_amount_usdt = data["balance"] * (
                data["bot_config"]["risk_per_trade_percentage"] / 100
            )
            distance_to_stop_perc = (current_price - stop_loss_price) / current_price

            if distance_to_stop_perc <= 0:
                return {
                    "error": "Invalid stop-loss distance.",
                    "position_size_usdt": 0,
                    "stop_loss_price": 0,
                }

            position_size_usdt = risk_amount_usdt / distance_to_stop_perc

            return {
                "position_size_usdt": round(position_size_usdt, 2),
                "stop_loss_price": round(stop_loss_price, 2),
            }
        except Exception as e:
            logging.error(f"RiskManagerAgent failed: {e}")
            return {"error": str(e), "position_size_usdt": 0, "stop_loss_price": 0}


class ResearchAgent:
    """
    Un agente especialista que busca noticias relevantes y analiza su sentimiento
    para dar una última luz verde o roja antes de una operación.
    """

    def __init__(self):
        self.logger = setup_logger()
        self.parser = JsonOutputParser(pydantic_object=NewsSentiment)
        self.prompt = self._create_prompt_template()
        self.llm = ChatOpenAI(temperature=0, model="gpt-4-turbo")
        self.search_tool = TavilySearch(max_results=3)
        self.chain = self.prompt | self.llm | self.parser

    def _create_prompt_template(self):
        return ChatPromptTemplate.from_template(
            """
            You are a Financial News Analyst. Your task is to analyze recent news for {trading_pair} and determine if there are any critical stories that should prevent a trade.

            **Format Instructions:**
            {format_instructions}

            **News Articles Found:**
            {news_articles}

            **Analysis Rules:**
            1.  Summarize the most important news headlines.
            2.  Determine the overall sentiment (Positive, Negative, Neutral).
            3.  Crucially, decide if the bot `should_proceed`. Set this to `False` ONLY if you find highly impactful negative news (e.g., major exchange hacks, new harsh regulations, security vulnerabilities). For normal market volatility or mixed news, set it to `True`.
            
            Provide your analysis.
            """
        )

    def invoke(self, trading_pair: str) -> dict:
        logging.info("-> ResearchAgent invoked.")
        # 1. Usar la herramienta para buscar noticias
        try:
            news_results = self.search_tool.invoke(
                f"latest news on {trading_pair} cryptocurrency"
            )
        except Exception as e:
            logging.error(f"Tavily search failed: {e}")
            return {
                "sentiment": "Neutral",
                "summary": "Could not fetch news.",
                "should_proceed": True,
            }

        self.logger.info(f"-> ResearchAgent news results: {news_results}")
        result = self.chain.invoke(
            {"trading_pair": trading_pair, "news_articles": news_results}
        )
        return result


class ExecutionAgent:
    """Un agente especialista que interactúa directamente con el exchange."""

    def __init__(self, binance_client: BinanceService):
        self.logger = setup_logger()
        self.binance_client = binance_client

    def buy_market_order(self, position_size_usdt: float, stop_loss_price: float):
        self.logger.info(
            f"EXECUTION AGENT: Recibida orden de COMPRA de {position_size_usdt} USDT con SL en {stop_loss_price}."
        )
        self.logger.info("Ejecutando orden de compra...")
        # Lógica real para llamar al cliente de Binance
        order_result = self.binance_client.execute_market_order(
            "BUY", position_size_usdt
        )
        if order_result is None:
            self.logger.error("Failed to execute buy order.")
            return {"status": "FAILED", "order_id": None}
        return order_result


__all__ = [
    "TrendAgent",
    "EntrySignalAgent",
    "RiskManagerAgent",
    "ResearchAgent",
    "ExecutionAgent",
]
