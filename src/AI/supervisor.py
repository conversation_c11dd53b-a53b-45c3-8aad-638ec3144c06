from typing import Annotated, Optional, Sequence, TypedDict

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage
from langchain_core.pydantic_v1 import BaseModel, Field
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph
from langgraph.graph.message import add_messages

from AI.agents import (
    EntrySignalAgent,
    ExecutionAgent,
    ResearchAgent,
    RiskManagerAgent,
    TrendAgent,
)
from services.binance_service import BinanceService
from services.ta_services import TAService
from utils.logger_setup import setup_logger


# --- 1. Definición del Estado y la Salida del Supervisor ---
class SupervisorState(TypedDict):
    market_data: dict
    bot_config: dict
    trend_analysis: Optional[dict]
    entry_signal: Optional[dict]
    trade_parameters: Optional[dict]
    news_analysis: Optional[dict]
    execution_result: Optional[dict]
    history: Optional[list]
    messages: Annotated[Sequence[BaseMessage], add_messages]


class SupervisorDecision(BaseModel):
    """La decisión del supervisor sobre a qué especialista delegar la tarea."""

    next_agent: str = Field(
        description="El nombre del siguiente agente a invocar. Opciones: 'TrendAgent', 'EntrySignalAgent', 'RiskManagerAgent', 'ResearchAgent', 'ExecutionAgent', 'END'"
    )
    reasoning: str = Field(description="Breve razonamiento para la decisión.")


# --- 2. La Clase del Supervisor ---
class SupervisorAgent:
    def __init__(self, binance_service: BinanceService, ta_service: TAService):
        self.binance_service = binance_service
        self.ta_service = ta_service
        self.logger = setup_logger()

        # Instanciamos a todos los especialistas
        self.trend_agent = TrendAgent()
        self.entry_signal_agent = EntrySignalAgent()
        self.risk_manager_agent = RiskManagerAgent()
        self.research_agent = ResearchAgent()
        self.execution_agent = ExecutionAgent(self.binance_service)

        # El LLM del supervisor ahora usa el modelo Pydantic para una salida estructurada
        self.llm = ChatOpenAI(
            temperature=0, model="gpt-5-nano-2025-08-07"
        ).with_structured_output(SupervisorDecision)

        # Construimos el gráfico al inicializar
        self.graph = self.create_supervisor_graph()

    def create_supervisor_graph(self):
        """Construye y compila el StateGraph del supervisor."""

        # --- Nodos del Gráfico ---
        def supervisor_node(state: SupervisorState) -> dict:
            """El nodo principal que decide el siguiente paso."""
            self.logger.info("Supervisor node invoked...")
            system_prompt = SystemMessage(
                content="""
                Eres el supervisor de un equipo de agentes de trading. Tu trabajo es orquestar el flujo de trabajo para decidir si abrir una posición LONG.
                Analiza el estado actual y decide a qué especialista llamar a continuación. El flujo debe ser:
                1. TrendAgent: Para determinar la tendencia.
                2. EntrySignalAgent: Si la tendencia es alcista.
                3. RiskManagerAgent: Si se encuentra una señal de compra.
                4. ResearchAgent: Como comprobación final de seguridad.
                5. ExecutionAgent: Si las noticias son seguras.
                Si en algún paso las condiciones no se cumplen, o si el proceso ha finalizado, elige 'END'.
                """
            )
            response = self.llm.invoke([system_prompt] + list(state["messages"]))
            # Devolvemos la decisión del supervisor como un mensaje de la IA
            # Since response is already a SupervisorDecision object from with_structured_output
            if hasattr(response, "json"):
                response_content = response.json()
            else:
                # Fallback for dict response
                import json

                response_content = json.dumps(response)
            return {"messages": [AIMessage(content=response_content)]}

        def market_data_node(state: SupervisorState) -> dict:
            """Obtiene los datos de mercado y los añade al estado."""
            self.logger.info("-> Market Data Node invoked.")
            market_data = self.ta_service.get_market_indicators()
            if market_data:
                return {"market_data": market_data.model_dump()}
            return {}

        def trend_node(state: SupervisorState) -> dict:
            self.logger.info("-> Trend Node invoked.")
            result = self.trend_agent.invoke(state["market_data"])
            return {
                "trend_analysis": result,
                "messages": [
                    HumanMessage(
                        content=f"Trend analysis complete: {result['trend_status']}"
                    )
                ],
            }

        def signal_node(state: SupervisorState) -> dict:
            self.logger.info("-> Signal Node invoked.")
            agent_input = {
                "current_market_data": state["market_data"],
                "analysis_history": state.get("history")
                or [],  # Usamos .get para manejar el primer ciclo
            }
            result = self.entry_signal_agent.invoke(agent_input)

            # Agregamos el resultado al historial
            current_history = state.get("history") or []
            new_history = current_history + [state["market_data"]]

            return {
                "entry_signal": result,
                "history": new_history[
                    -5:
                ],  # Mantenemos solo los últimos 5 para que no crezca
                "messages": [
                    HumanMessage(
                        content=f"Signal analysis complete: {result['signal']}"
                    )
                ],
            }

        def risk_node(state: SupervisorState) -> dict:
            self.logger.info("-> Risk Node invoked.")
            # Get current balance from binance service
            balance_info = self.binance_service.get_account_balance("USDT")
            current_balance = (
                float(balance_info["free"]) if balance_info else 1000.0
            )  # fallback

            # RiskManagerAgent expects a single dict with market_data, bot_config, and balance
            risk_data = {
                "market_data": state["market_data"],
                "bot_config": state["bot_config"],
                "balance": current_balance,
            }
            result = self.risk_manager_agent.invoke(risk_data)
            return {
                "trade_parameters": result,
                "messages": [HumanMessage(content="Risk parameters calculated.")],
            }

        def research_node(state: SupervisorState) -> dict:
            self.logger.info("-> Research Node invoked.")
            result = self.research_agent.invoke(state["market_data"]["trading_pair"])
            return {
                "news_analysis": result,
                "messages": [
                    HumanMessage(
                        content=f"News analysis complete: {result['sentiment']}"
                    )
                ],
            }

        def execution_node(state: SupervisorState) -> dict:
            self.logger.info("-> Execution Node invoked.")
            params = state.get("trade_parameters")
            if params is None:
                self.logger.error("No trade parameters available for execution")
                return {
                    "execution_result": {
                        "status": "FAILED",
                        "error": "No trade parameters",
                    },
                    "messages": [
                        HumanMessage(content="Execution failed: No trade parameters")
                    ],
                }

            result = self.execution_agent.buy_market_order(
                params["position_size_usdt"], params["stop_loss_price"]
            )
            return {
                "execution_result": result,
                "messages": [HumanMessage(content="Execution complete.")],
            }

        # --- Construcción del Gráfico ---
        graph = StateGraph(SupervisorState)
        graph.add_node("supervisor", supervisor_node)
        graph.add_node("get_market_data", market_data_node)
        graph.add_node("TrendAgent", trend_node)
        graph.add_node("EntrySignalAgent", signal_node)
        graph.add_node("RiskManagerAgent", risk_node)
        graph.add_node("ResearchAgent", research_node)
        graph.add_node("ExecutionAgent", execution_node)

        # El flujo siempre empieza obteniendo los datos
        graph.add_edge("get_market_data", "supervisor")

        # Enrutador condicional
        def router(state: SupervisorState):
            # La decisión del supervisor está en el último mensaje
            last_message_content = state["messages"][-1].content
            if isinstance(last_message_content, str):
                decision = SupervisorDecision.parse_raw(last_message_content)
            else:
                # Handle case where content might be a list or dict
                decision = SupervisorDecision.parse_obj(last_message_content)
            self.logger.info(
                f"Supervisor decision: Route to {decision.next_agent}. Reason: {decision.reasoning}"
            )
            return decision.next_agent

        graph.add_conditional_edges("supervisor", router)

        # Todos los especialistas devuelven el control al supervisor
        graph.add_edge("TrendAgent", "supervisor")
        graph.add_edge("EntrySignalAgent", "supervisor")
        graph.add_edge("RiskManagerAgent", "supervisor")
        graph.add_edge("ResearchAgent", "supervisor")
        graph.add_edge("ExecutionAgent", "supervisor")

        # El punto de entrada es obtener los datos
        graph.set_entry_point("get_market_data")

        return graph.compile()
