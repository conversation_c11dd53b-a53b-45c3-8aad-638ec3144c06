import yaml

from models.bot_config import Bot<PERSON>onfig


def load_config(path):
    """Loads the YAML configuration file."""
    try:
        with open(path, "r") as f:
            file_content = f.read()
            config_data = yaml.safe_load(file_content)
            return BotConfig(**config_data)

    except Exception as e:
        raise Exception(f"Failed to load config from {path}: {e}")
