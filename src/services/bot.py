import html
import os
import threading
import time

from binance.ws.streams import ThreadedWebsocketManager  # type: ignore

from AI.supervisor import SupervisorAgent
from services.binance_service import BinanceService
from services.position_validator import PositionValidator
from services.ta_services import TAService
from services.telegram_service import TelegramService  # type: ignore
from utils.load_config import load_config
from utils.logger_setup import setup_logger
from utils.state_manager import StateManager


class TradingBot:
    def __init__(self, config_path="src/config/config.yaml"):
        self.config = load_config(config_path)
        self.logger = setup_logger(self.config.log_file)
        self.state_manager = StateManager(self.config.state_file)
        self.binance_client = BinanceService(self.config)
        self.ta_service = TAService(self.binance_client)
        self.position_validator = PositionValidator(
            self.binance_client, self.state_manager, self.config
        )
        self.telegram_token = os.getenv("TELEGRAM_BOT_TOKEN")
        self.telegram_chat_id = os.getenv("TELEGRAM_CHAT_ID")
        # Initialize Telegram service
        self.telegram_service = TelegramService(
            self.telegram_token, self.telegram_chat_id
        )
        self.twm = None
        self.indicators_ready = (
            threading.Event()
        )  # Para saber cuándo se han calculado por primera vez
        self.symbol = self.config.trading_pair
        self.last_entry_ai_check = 0
        # El bot crea la instancia del supervisor
        self.supervisor_agent = SupervisorAgent(self.binance_client, self.ta_service)
        self.supervisor_app = self.supervisor_agent.graph
        # Store socket names for potential restart
        self.sockets = []

    # <<< Función para actualizar indicadores periódicamente >>>
    def _update_technical_indicators(self, msg=None):
        """
        Se ejecuta en un hilo separado para calcular y actualizar
        los indicadores técnicos a intervalos regulares.
        """
        if msg is None and not self.indicators_ready.is_set():
            self.logger.info("Updating technical indicators...")
            self.market_indicators = self.ta_service.get_market_indicators()
            if self.market_indicators:
                if not self.indicators_ready.is_set():
                    self.indicators_ready.set()
                self.look_for_new_entry(
                    self.market_indicators.price_indicators.current_price
                )
            return

        is_kline_closed = msg["x"]
        self.logger.debug(
            f"Price update: {self.current_price} | Is Closed: {is_kline_closed}"
        )

        # <<< Solo actuar si la vela está cerrada >>>
        if is_kline_closed:
            self.logger.info(
                f"Kline for {msg['i']} interval closed at {self.current_price}."
            )

            try:
                self.logger.info("Updating technical indicators...")
                self.market_indicators = self.ta_service.get_indicators_value()
                if self.market_indicators:
                    if not self.indicators_ready.is_set():
                        self.indicators_ready.set()
                    self.look_for_new_entry(
                        self.market_indicators.price_indicators.current_price
                    )
            except Exception as e:
                self.logger.error(
                    f"Error updating technical indicators: {e}", exc_info=True
                )

    def run(self):
        """The main operational loop of the bot."""
        self.logger.info("Starting DCA Trading Bot...")

        # actualizar indicadores
        self._update_technical_indicators()

        # Check for existing position and analyze with AI
        self._analyze_startup_position()

        # Send ready notification
        self.telegram_service.send_system_status(
            "READY", "DCA Trading Bot is now running"
        )

        self.twm = ThreadedWebsocketManager(
            api_key=os.getenv("BINANCE_API_KEY_TESTNET"),
            api_secret=os.getenv("BINANCE_SECRET_KEY_TESTNET"),
        )
        # Initialise its internal loop
        self.twm.start()

        # Define el stream que queremos escuchar (ticker del par de trading)
        # y le asigna la función que procesará los mensajes.
        sts = self.twm.start_symbol_ticker_socket(
            callback=self._process_websocket_message, symbol=self.symbol
        )
        self.sockets.append(("ticker", sts, self.symbol))

        # Start kline socket
        kline_socket = self.twm.start_kline_socket(
            callback=self._process_websocket_message,
            symbol=self.symbol,
            interval=self.config["kline_interval"],
        )
        self.sockets.append(("kline", kline_socket, self.symbol))

        self.logger.info(
            f"WebSocket stream '{sts}' and '{kline_socket}' started. Bot is now live."
        )
        self.logger.info("Waiting for initial indicator calculation...")
        self.indicators_ready.wait()  # Espera a que los indicadores se calculen por primera vez
        self.logger.info("Indicators are ready. Bot is fully operational.")

        try:
            self.twm.join()
        except KeyboardInterrupt:
            self.logger.info("Bot stopped by user.")
        finally:
            self.logger.info("Stopping WebSocket manager...")
            self.twm.stop()

    # <<< El procesador de mensajes es ahora más limpio >>>
    def _process_websocket_message(self, msg):
        """Procesa cada mensaje de ticker recibido del WebSocket."""
        if msg.get("e") == "error":
            self.logger.error(f"WebSocket Error: {msg.get('m')}")
            return

        if msg.get("e") == "24hrTicker":  # Ticker message
            self.logger.debug(f"Ticker message received: {msg}")
            self.current_price = float(msg["c"])
            self.logger.info(f"{self.symbol} Current Price {self.current_price:.2f}")
            self.main_loop(self.current_price)
            return
        elif msg.get("e") == "kline":  # Kline message
            kline = msg["k"]
            self._update_technical_indicators(kline)
            return

    def _analyze_startup_position(self):
        """Analyze existing position when bot starts up."""
        try:
            position = self.state_manager.get_position()

            if position:
                self.logger.info(
                    "Found existing position at startup. Analyzing with AI..."
                )

                # Get current market data
                current_price = float(self.binance_client.get_market_price())

                # Validate position integrity first
                if self.position_validator.validate_position_integrity(position):
                    # Analyze with AI
                    analysis = self.ai_engine.analyze_startup_position(
                        position, self.market_indicators
                    )

                    # Log the analysis
                    self.logger.info(f"Startup AI Analysis: {analysis}")

                    # Send Telegram notification about existing position
                    pnl_percentage = (
                        (current_price - position["average_price"])
                        / position["average_price"]
                    ) * 100
                    message = "📊 <b>EXISTING POSITION DETECTED</b>\n\n"
                    message += f"💰 <b>Quantity:</b> {position['total_base_quantity']:.8f} ETH\n"
                    message += (
                        f"💵 <b>Entry Price:</b> ${position['average_price']:.2f}\n"
                    )
                    message += f"📈 <b>Current Price:</b> ${current_price:.2f}\n"
                    message += f"📊 <b>Current P&L:</b> {pnl_percentage:+.2f}%\n"
                    message += f"🎯 <b>Take Profit:</b> ${position['take_profit_price']:.2f}\n\n"
                    message += f"🤖 <b>AI Recommendation:</b> {analysis.get('action', 'hold').upper()}\n"
                    if analysis.get("reasoning"):
                        reasoning_text = analysis["reasoning"]
                        reasoning_text = html.escape(
                            reasoning_text
                        )  # evita que < y > rompan el HTML
                        reasoning = (
                            reasoning_text[:150] + "..."
                            if len(reasoning_text) > 150
                            else reasoning_text
                        )
                        message += f"💭 <b>Reasoning:</b> {reasoning}"

                    self.telegram_service.send_message(message)

                    # Act on AI recommendation if it's a sell signal
                    if analysis.get("action") == "sell_now":
                        self.logger.warning(
                            "AI recommends selling position at startup!"
                        )
                        self.telegram_service.send_ai_alert(
                            f"AI recommends SELLING position at startup: {analysis.get('reasoning', 'No reason provided')}",
                            "WARNING",
                        )
                        # Note: We don't auto-sell at startup, just alert

                else:
                    self.logger.warning("Position validation failed at startup")
                    self.telegram_service.send_ai_alert(
                        "Position validation failed at startup", "WARNING"
                    )

            else:
                self.logger.info("No existing position found at startup")
                self.telegram_service.send_system_status(
                    "NO_POSITION", "No existing position found. Ready for new entries."
                )

        except Exception as e:
            self.logger.error(f"Error analyzing startup position: {e}")
            self.telegram_service.send_ai_alert(
                f"Startup analysis error: {str(e)[:200]}", "ERROR"
            )

    def main_loop(self, current_price):
        """A single cycle of the bot's logic."""
        position = self.state_manager.get_position()

        if position:
            # Validate position integrity before managing it
            if self.position_validator.validate_position_integrity(position):
                self.manage_existing_position(position, current_price)
            else:
                self.logger.warning(
                    "Position validation failed. Attempting recovery..."
                )
                self.position_validator.log_position_status(position)

                # Try to sync with Binance
                if self.position_validator.sync_position_with_binance(position):
                    self.logger.info(
                        "Position recovered through sync. Continuing management."
                    )
                    self.manage_existing_position(position, current_price)
                else:
                    # Try to recover from order history
                    recovered_position = (
                        self.position_validator.recover_position_from_orders()
                    )
                    if recovered_position:
                        self.logger.info("Position recovered from order history.")
                        self.state_manager.set_position(recovered_position)
                        self.manage_existing_position(recovered_position, current_price)
                    else:
                        self.logger.error(
                            "Could not recover position. Clearing invalid position."
                        )
                        self.state_manager.clear_position(position)

    def manage_existing_position(self, position, current_price):
        """Handles logic for an already open position, now with AI intervention."""
        current_price = float(current_price)
        self.logger.info(
            f"Managing position. Avg Price: {position['average_price']:.2f}, Curr Price: {current_price:.2f}, TP: {position['take_profit_price']:.2f}"
        )

        # NEW >> Check for Stop-Loss
        if current_price <= position["stop_loss_price"]:
            self.logger.warning(
                f"Stop-Loss triggered at {position['stop_loss_price']:.2f}! Selling position."
            )
            self.close_position(position, current_price, "Stop-Loss")
            return

        # Periodic position validation (every 30 minutes)
        last_validation = position.get("last_validation_ts", 0)
        if time.time() - last_validation > 1800:  # 30 minutes
            self.logger.info("Performing periodic position validation...")
            if not self.position_validator.validate_position_integrity(position):
                self.logger.error(
                    "Position validation failed during management. Attempting sync."
                )
                self.position_validator.log_position_status(position)
                if not self.position_validator.sync_position_with_binance(position):
                    self.logger.error(
                        "Position sync failed. Clearing invalid position."
                    )
                    self.state_manager.clear_position(position)
                    return
            position["last_validation_ts"] = time.time()
            self.state_manager.set_position(position)

        # Periodically ask AI for advice on the open position
        if (
            time.time() - position.get("last_ai_check_ts", 0)
            > self.config["ai_analysis_interval_minutes"] * 60
        ):
            self.logger.info("Querying AI for analysis on open position.")
            position["last_ai_check_ts"] = time.time()
            analysis = self.ai_engine.analyze_open_position(
                position, self.market_indicators
            )

            if analysis.get("action") == "sell_now":
                self.logger.warning(
                    f"AI recommends selling position! Reason: {analysis.get('reasoning')}"
                )
                success = self.close_position(position, current_price, "AI Sell Signal")
                if success:
                    return
                else:
                    self.logger.error(
                        "Failed to execute AI-recommended sell order. Position remains open."
                    )

            # Dynamically adjust parameters based on AI feedback
            self._adjust_params_from_ai(position, analysis)

        # 1. Update and check trailing stop
        self._update_trailing_stop(position, current_price)
        if self._is_trailing_stop_triggered(position, current_price):
            self.logger.info(
                f"Trailing stop triggered at {position['trailing_stop_price']:.2f}. Selling position."
            )
            success = self.close_position(position, current_price, "Trailing Stop")
            if success:
                return
            else:
                self.logger.error(
                    "Failed to execute trailing stop sell order. Position remains open."
                )

        # 2. Check for take profit
        if current_price >= position["take_profit_price"]:
            self.logger.info(
                f"Take profit target of {position['take_profit_price']:.2f} reached. Selling."
            )
            success = self.close_position(position, current_price, "Take Profit")
            if success:
                return
            else:
                self.logger.error(
                    "Failed to execute take profit sell order. Position remains open."
                )

        # 3. Check for new DCA order
        next_dca_price = position["average_price"] * (
            1 - self.config["dca_deviation_percentage"] / 100
        )
        if (
            current_price <= next_dca_price
            and len(position["dca_orders"]) < self.config["max_dca_orders"]
        ):
            self.logger.info(
                f"Price dropped to DCA level ({next_dca_price:.2f}). Placing new buy order."
            )
            success = self.execute_dca_order(position, current_price)
            if not success:
                self.logger.warning(
                    "Failed to execute DCA order despite price trigger."
                )

        # Save any changes made (like trailing stop updates or AI param changes)
        self.state_manager.set_position(position)

    def _adjust_params_from_ai(self, position, analysis):
        """Updates position parameters based on AI analysis."""
        if analysis.get("new_take_profit_percentage"):
            new_tp_perc = analysis["new_take_profit_percentage"]
            position["take_profit_percentage"] = new_tp_perc
            position["take_profit_price"] = position["average_price"] * (
                1 + new_tp_perc / 100
            )
            self.logger.info(
                f"AI adjusted Take Profit to {new_tp_perc}%. New TP Price: {position['take_profit_price']:.2f}"
            )

        if analysis.get("new_trailing_stop_deviation_percentage"):
            new_ts_dev = analysis["new_trailing_stop_deviation_percentage"]
            position["trailing_stop_deviation_percentage"] = new_ts_dev
            self.logger.info(f"AI adjusted Trailing Stop Deviation to {new_ts_dev}%.")

    def look_for_new_entry(self, current_price):
        """Handles logic for entering a new position."""
        self.logger.info("No open position. Looking for a new entry opportunity.")

        # 2. Preparas el estado inicial para ESTE ciclo de decisión
        initial_state = {
            "messages": [
                {"role": "user", "content": "Analyze the market for a new LONG entry."}
            ],
            "bot_config": self.config,
        }

        # 3. Invocas el gráfico con el estado completo
        final_state = self.supervisor_app.invoke(initial_state)
        self.logger.info(f"Final state: {final_state}")
        if final_state.get("final_decision") == "execute_order":
            self.logger.info("Executing new order based on AI recommendation.")
            self.open_new_position(
                final_state["trade_parameters"]["position_size_usdt"],
                final_state["trade_parameters"]["stop_loss_price"],
                final_state["initial_analysis"],
            )
            return True

    # def open_new_position(self, order_size_quote, price, ai_analysis=None):
    #     """Executes the initial buy order and creates a new position state."""
    #     price = float(price)
    #     self.logger.info(
    #         f"Attempting to open new position with {order_size_quote:.2f} USDT at price {price:.2f}"
    #     )

    #     # 3. Determine Stop-Loss price
    #     stop_loss_distance = (
    #         self.market_indicators.volatility_indicators.atr
    #         * self.config["atr_multiplier_for_stop_loss"]
    #     )
    #     stop_loss_price = price - stop_loss_distance

    #     # 4. Calculate Position Size based on fixed risk
    #     usdt_balance = self.binance_client.get_account_balance()
    #     risk_amount_usdt = usdt_balance * (
    #         self.config["risk_per_trade_percentage"] / 100
    #     )

    #     # The distance from entry to stop-loss in percentage
    #     distance_to_stop_perc = (price - stop_loss_price) / price
    #     if distance_to_stop_perc <= 0:
    #         self.logger.error("Invalid stop-loss distance. Aborting trade.")
    #         return

    #     position_size_usdt = risk_amount_usdt / distance_to_stop_perc

    #     self.logger.info(f"Account Balance: {usdt_balance:.2f} USDT")
    #     self.logger.info(
    #         f"Risking {self.config['risk_per_trade_percentage']}% = {risk_amount_usdt:.2f} USDT"
    #     )
    #     self.logger.info(
    #         f"Stop-Loss will be at {stop_loss_price:.2f} ({stop_loss_distance:.2f} distance)"
    #     )
    #     self.logger.info(f"Calculated Position Size: {position_size_usdt:.2f} USDT")

    #     # Execute the buy order using quote amount for better precision
    #     order_result = self.binance_client.execute_market_order_quote(
    #         SIDE_BUY, position_size_usdt
    #     )

    #     if order_result is None:
    #         self.logger.error(
    #             "Failed to execute initial buy order. Position not opened."
    #         )
    #         return False

    #     # Extract actual execution details from order result
    #     executed_qty = float(order_result.get("executedQty", 0))
    #     cumulative_quote_qty = float(
    #         order_result.get("cummulativeQuoteQty", order_size_quote)
    #     )

    #     # Debug logging to see what Binance actually returned
    #     self.logger.info(f"Order execution details:")
    #     self.logger.info(f"  Order ID: {order_result.get('orderId')}")
    #     self.logger.info(f"  Status: {order_result.get('status')}")
    #     self.logger.info(f"  Executed Qty: {executed_qty}")
    #     self.logger.info(f"  Cumulative Quote Qty: {cumulative_quote_qty}")
    #     self.logger.info(f"  Original Qty: {order_result.get('origQty', 'N/A')}")

    #     # Validate the execution makes sense
    #     if executed_qty <= 0:
    #         self.logger.error(f"Invalid executed quantity: {executed_qty}")
    #         return False

    #     if cumulative_quote_qty <= 0:
    #         self.logger.error(
    #             f"Invalid cumulative quote quantity: {cumulative_quote_qty}"
    #         )
    #         return False

    #     actual_avg_price = cumulative_quote_qty / executed_qty

    #     # Sanity check: price should be reasonable
    #     current_market_price = float(self.binance_client.get_market_price())
    #     if (
    #         abs(actual_avg_price - current_market_price) > current_market_price * 0.1
    #     ):  # 10% tolerance
    #         self.logger.warning(
    #             f"Execution price {actual_avg_price:.2f} differs significantly from market price {current_market_price:.2f}"
    #         )

    #     # Sanity check: quantity should match expected order size
    #     expected_qty = order_size_quote / current_market_price
    #     if abs(executed_qty - expected_qty) > expected_qty * 0.1:  # 10% tolerance
    #         self.logger.warning(
    #             f"Executed quantity {executed_qty:.8f} differs from expected {expected_qty:.8f}"
    #         )

    #     order = self.binance_client.execute_market_order_quote(
    #         SIDE_BUY, order_size_quote
    #     )

    #     if not self.binance_client._validate_order_response(
    #         order, SIDE_BUY, order_size_quote
    #     ):
    #         self.logger.error("Order response validation failed")
    #         return False

    #     # Create new position
    #     position_data = {
    #         "entry_price": actual_avg_price,
    #         "average_price": actual_avg_price,
    #         "total_base_quantity": executed_qty,
    #         "total_quote_spent": cumulative_quote_qty,
    #         "take_profit_percentage": self.config["take_profit_percentage"],
    #         "trailing_stop_deviation_percentage": self.config[
    #             "trailing_stop_deviation_percentage"
    #         ],
    #         "take_profit_price": actual_avg_price
    #         * (1 + self.config["take_profit_percentage"] / 100),
    #         "dca_orders": [],
    #         "trailing_stop_activated": False,
    #         "trailing_stop_peak_price": 0,
    #         "trailing_stop_price": 0,
    #         "status": "open",
    #         "last_ai_check_ts": time.time(),
    #         "order_id": order_result.get("orderId"),
    #     }

    #     self.state_manager.set_position(position_data)
    #     self.logger.info(
    #         f"New position opened successfully. Average price: {actual_avg_price:.2f}, Quantity: {executed_qty:.8f}"
    #     )

    #     # Send Telegram notification
    #     self.telegram_service.send_position_opened(position_data, ai_analysis)

    #     return True

    # def execute_dca_order(self, position, price):
    #     """Executes a subsequent DCA (safety) order and updates the position."""
    #     last_order_size = (
    #         position["dca_orders"][-1]["quote_size"]
    #         if position["dca_orders"]
    #         else self.config["initial_order_size_quote"]
    #     )
    #     dca_order_size_quote = (
    #         last_order_size * self.config["dca_order_size_multiplier"]
    #     )

    #     self.logger.info(
    #         f"Attempting DCA order of {dca_order_size_quote:.2f} USDT at price {price:.2f}"
    #     )

    #     # Execute the DCA buy order
    #     order_result = self.binance_client.execute_market_order_quote(
    #         SIDE_BUY, dca_order_size_quote
    #     )

    #     if order_result is None:
    #         self.logger.error("Failed to execute DCA order. Position not updated.")
    #         return False

    #     # Extract actual execution details
    #     executed_qty = float(order_result.get("executedQty", 0))
    #     cumulative_quote_qty = float(
    #         order_result.get("cummulativeQuoteQty", dca_order_size_quote)
    #     )
    #     actual_avg_price = (
    #         cumulative_quote_qty / executed_qty if executed_qty > 0 else price
    #     )

    #     # Update position with actual executed values
    #     position["dca_orders"].append(
    #         {
    #             "price": actual_avg_price,
    #             "quote_size": cumulative_quote_qty,
    #             "base_quantity": executed_qty,
    #             "order_id": order_result.get("orderId"),
    #         }
    #     )
    #     position["total_quote_spent"] += cumulative_quote_qty
    #     position["total_base_quantity"] += executed_qty
    #     position["average_price"] = (
    #         position["total_quote_spent"] / position["total_base_quantity"]
    #     )
    #     position["take_profit_price"] = position["average_price"] * (
    #         1 + position["take_profit_percentage"] / 100
    #     )

    #     self.state_manager.set_position(position)
    #     self.logger.info(
    #         f"DCA order filled successfully. New avg price: {position['average_price']:.2f}, New TP: {position['take_profit_price']:.2f}"
    #     )

    #     # Send Telegram notification for DCA order
    #     dca_order_info = {
    #         "price": actual_avg_price,
    #         "quote_size": cumulative_quote_qty,
    #         "base_quantity": executed_qty,
    #     }
    #     self.telegram_service.send_dca_order(position, dca_order_info)

    #     return True

    # def close_position(self, position, price, reason):
    #     """Executes a market sell for the entire position and records the trade."""
    #     self.logger.info(
    #         f"Attempting to close position of {position['total_base_quantity']:.8f} for reason: {reason}"
    #     )

    #     # Execute the sell order
    #     order_result = self.binance_client.execute_market_order(
    #         SIDE_SELL, position["total_base_quantity"]
    #     )

    #     if order_result is None:
    #         self.logger.error("Failed to execute sell order. Position remains open.")
    #         return False

    #     # Extract actual execution details
    #     executed_qty = float(order_result.get("executedQty", 0))
    #     cumulative_quote_qty = float(order_result.get("cummulativeQuoteQty", 0))
    #     actual_exit_price = (
    #         cumulative_quote_qty / executed_qty if executed_qty > 0 else price
    #     )

    #     # Calculate actual profit based on executed amounts
    #     profit = cumulative_quote_qty - position["total_quote_spent"]
    #     profit_percentage = (profit / position["total_quote_spent"]) * 100

    #     self.logger.info(
    #         f"Position closed successfully. Exit price: {actual_exit_price:.2f}, Profit: {profit:.2f} USDT ({profit_percentage:.2f}%)"
    #     )

    #     closed_trade_data = {
    #         **position,
    #         "exit_price": actual_exit_price,
    #         "profit": profit,
    #         "profit_percentage": profit_percentage,
    #         "close_reason": reason,
    #         "status": "closed",
    #         "close_order_id": order_result.get("orderId"),
    #         "actual_sold_quantity": executed_qty,
    #         "actual_received_quote": cumulative_quote_qty,
    #     }
    #     self.state_manager.clear_position(closed_trade_data)

    #     # Send Telegram notification for position closure
    #     self.telegram_service.send_position_closed(
    #         position, actual_exit_price, profit, reason
    #     )

    #     return True

    def _update_trailing_stop(self, position, current_price):
        """Updates the trailing stop price based on the current price."""
        activation_price = position["average_price"] * (
            1 + self.config["trailing_stop_activation_percentage"] / 100
        )

        if (
            not position["trailing_stop_activated"]
            and current_price >= activation_price
        ):
            position["trailing_stop_activated"] = True
            position["trailing_stop_peak_price"] = current_price
            self.logger.info(f"Trailing stop activated at price {current_price:.2f}")

        if position["trailing_stop_activated"]:
            if current_price > position["trailing_stop_peak_price"]:
                position["trailing_stop_peak_price"] = current_price

            trailing_dev_perc = position["trailing_stop_deviation_percentage"]
            position["trailing_stop_price"] = position["trailing_stop_peak_price"] * (
                1 - trailing_dev_perc / 100
            )
            self.logger.debug(
                f"Trailing stop updated. Peak: {position['trailing_stop_peak_price']:.2f}, Stop: {position['trailing_stop_price']:.2f}"
            )

    def _is_trailing_stop_triggered(self, position, current_price):
        """Checks if the current price has triggered the trailing stop."""
        return (
            position["trailing_stop_activated"]
            and current_price <= position["trailing_stop_price"]
        )
