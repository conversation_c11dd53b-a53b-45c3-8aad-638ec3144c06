import logging
from typing import Optional, Dict, Any, List, TypedDict
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from langgraph.checkpoint.memory import InMemorySaver
from langchain_core.messages import BaseMessage, HumanMessage, AIMessage
from langchain_core.messages.utils import (
    trim_messages,
    count_tokens_approximately
)
from langgraph.prebuilt import create_react_agent
from models.IA.position_analysis import PositionAnalysis
from models.IA.entry_analysis import EntryAnalysis
from utils import google_news
import utils.fear_and_greed_utils as fg
import time
import json
from datetime import datetime, timedelta
from models import MarketIndicators
from constants.openai_models import OpenAIModels
from constants.prompt_templates import TradingPromptTemplates, PromptType
from pydantic import BaseModel, Field
from services.ta_services import TAService


logger = logging.getLogger()

# --- 1. Definición del Estado del Gráfico ---
# Este diccionario llevará la información a través de todos los nodos.
class State(TypedDict):
    market_data: dict
    initial_analysis: Optional[dict]
    research_data: Optional[str]
    final_decision: str

# --- 2. Definición de la Salida Estructurada de la IA ---
# Usamos Pydantic para asegurar que la IA siempre responda en el formato correcto.
class EntryAnalysis(BaseModel):
    recommendation: str = Field(description="Options: 'BUY', 'HOLD'")
    confidence_score: float = Field(description="A float between 0.0 and 1.0.")
    reasoning: str = Field(description="Brief reasoning for the decision.")

class AIEngine:
    def __init__(self, config, ta_service: TAService):
        self.config = config
        self.llm = ChatOpenAI(
            temperature=1, model=OpenAIModels.GPT_5_NANO
        )
        self.entry_parser = JsonOutputParser(pydantic_object=EntryAnalysis)
        self.position_parser = JsonOutputParser(pydantic_object=PositionAnalysis)

        # Initialize prompt templates using the new architecture
        self.prompt_templates = TradingPromptTemplates()
        self.entry_prompt = self._create_prompt_from_template(PromptType.ENTRY_ANALYSIS)
        self.position_prompt = self._create_prompt_from_template(PromptType.POSITION_ANALYSIS)
        self.startup_prompt = self._create_prompt_from_template(PromptType.STARTUP_ANALYSIS)

        self.news_fetcher = google_news
        self.ta_service = ta_service
        # Initialize LangGraph components
        self.checkpointer = InMemorySaver()
        self.workflow = StateGraph(State)

        # This function will be called every time before the node that calls LLM
    def pre_model_hook(self,state):
        trimmed_messages = trim_messages(
            state["messages"],
            strategy="last",
            token_counter=count_tokens_approximately,
            max_tokens=384,
            start_on="IAAnalisis",
            end_on=("IAAnalisis", "tool"),
        )
        return {"llm_input_messages": trimmed_messages}
    
    def initial_analysis(self, state: State, analysis_type, position=None) -> State:
        """Nodo 2: Llama a la IA con el prompt de análisis inicial."""
        logging.info("--- Entrando en el nodo: initial_analysis ---")
        
        if analysis_type == "entry":
            prompt = self._create_prompt_from_template(PromptType.ENTRY_ANALYSIS)
            parser = self.entry_parser
        elif  analysis_type == "position":
            prompt = self._create_prompt_from_template(PromptType.POSITION_ANALYSIS)
            parser = self.position_parser
        else:
            prompt = self._create_prompt_from_template(PromptType.STARTUP_ANALYSIS)
            parser = self.position_parser
        
        chain = prompt | self.llm | parser
        result = chain.invoke(state['market_data'])
        
        logging.info(f"Análisis inicial de la IA: {result}")
        state['initial_analysis'] = result
        return state

    def _conditional_node(self):
        """Create a conditional node to decide the next action."""
        pass

    def collect_market_data(self, state: State) -> State:
        """Nodo 1: Recopila todos los indicadores del mercado."""
        market_indicators = self.ta_service.get_indicators_value().model_dump()
        state['market_data'] = market_indicators
        return state
    
    def _create_prompt_from_template(self, prompt_type: PromptType) -> ChatPromptTemplate:
        """
        Create a ChatPromptTemplate from a template type.

        Args:
            prompt_type: The type of prompt template to create

        Returns:
            ChatPromptTemplate: Configured prompt template
        """
        template = self.prompt_templates.get_template(prompt_type)

        # Determine which parser to use based on prompt type
        if prompt_type == PromptType.ENTRY_ANALYSIS:
            format_instructions = self.entry_parser.get_format_instructions()
        else:  # POSITION_ANALYSIS or STARTUP_ANALYSIS
            format_instructions = self.position_parser.get_format_instructions()

        return ChatPromptTemplate.from_template(
            template=template.template,
            partial_variables={"format_instructions": format_instructions}
        )
    
    def start_workflow(self):
        """Start the LangGraph workflow."""
        # Añadir los nodos
        self.workflow.add_node("collect_market_data", "collect_market_data")
        self.workflow.add_node("initial_analysis", "initial_analysis")
        self.workflow.add_node("research_news", "research_news")
        self.workflow.add_node("reevaluate_decision", "reevaluate_decision")
        self.workflow.add_node("execute_order", "execute_order")
        self.workflow.add_node("discard_trade", "discard_trade")
        
        
        # Definir el punto de entrada
        self.workflow.set_entry_point("collect_market_data")

        # Añadir las aristas (conexiones)
        self.workflow.add_edge("collect_market_data", "initial_analysis")
        self.workflow.add_edge("research_news", "reevaluate_decision")

        # La arista después de la reevaluación vuelve a la encrucijada
        self.workflow.add_edge("reevaluate_decision", "initial_analysis") 

        # Añadir la arista condicional
        self.workflow.add_conditional_edges(
            "initial_analysis",
            decide_next_step,
            {
                "execute_order": "execute_order",
                "discard_trade": "discard_trade",
                "research_news": "research_news"
            }
        )

        # Definir los puntos finales del gráfico
        workflow.add_edge("execute_order", END)
        workflow.add_edge("discard_trade", END)

        # Compilar el gráfico
        app = workflow.compile()

        # Ejecutar el gráfico con un estado inicial vacío
        initial_state = {"final_decision": "NONE"}
        final_state = app.invoke(initial_state)

        print("\n--- Resultado Final del Gráfico ---")
        print(final_state)