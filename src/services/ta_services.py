from typing import Any, Dict, Optional

import pandas as pd

# Asumimos que estos modelos y servicios ya existen
from models.market_indicators import (
    BollingerBands,
    FearAndGreed,
    MarketIndicators,
    MomentumIndicators,
    OverlapStudies,
    PriceIndicators,
    VolatilityIndicators,
)
from services.binance_service import BinanceService
from utils.fear_and_greed_utils import get_fear_and_greed_index
from utils.talib_wrapper import TAWrapper


class TAService:
    """
    Un servicio sin estado para calcular y devolver un snapshot completo
    de los indicadores del mercado, siguiendo principios de código limpio.
    """

    # Configuración declarativa para los indicadores que devuelven una única serie.
    INDICATOR_CONFIG = {
        "sma": {"func": "SMA", "params": {"timeperiod": 200}},
        "ema": {"func": "EMA", "params": {"timeperiod": 200}},
        "rsi": {"func": "RSI", "params": {"timeperiod": 14}},
        "atr": {"func": "ATR", "params": {"timeperiod": 14}},
        "sar": {"func": "SAR", "params": {"acceleration": 0.02, "maximum": 0.2}},
        "avg_price": {"func": "AVGPRICE", "params": {}},
        "median_price": {"func": "MEDPRICE", "params": {}},
    }

    def __init__(self, binance_client: BinanceService):
        self.binance_client = binance_client

    def _get_last_value(self, series: pd.Series) -> Optional[float]:
        """
        Función auxiliar centralizada para extraer y validar de forma segura
        el último valor de una serie de pandas.
        """
        if series is not None and not series.empty and pd.notna(series.iloc[-1]):
            return round(float(series.iloc[-1]), 4)
        return None

    def get_market_indicators(
        self, symbol: str = "ETHUSDT"
    ) -> Optional[MarketIndicators]:
        """
        Orquesta la obtención de datos y el cálculo de todos los indicadores,
        devolviendo un objeto MarketIndicators validado.
        """
        # 1. Obtener los datos base
        historical_data = self.binance_client.get_historical_data()
        fear_and_greed_data = get_fear_and_greed_index()

        if historical_data.empty:
            print("Could not fetch historical data from Binance.")
            return None

        ta_wrapper = TAWrapper(historical_data)
        indicator_results = {}

        # 2. Calcular todos los indicadores de una sola serie usando la configuración
        for name, config in self.INDICATOR_CONFIG.items():
            try:
                indicator_series = getattr(ta_wrapper, str(config["func"]))(
                    **config["params"]
                )
                indicator_results[name] = self._get_last_value(indicator_series)
            except Exception as e:
                print(f"Error calculating indicator {name}: {e}")
                indicator_results[name] = None

        # 3. Calcular BBands por separado ya que devuelve un DataFrame
        bbands_results = {}
        try:
            bbands_df = ta_wrapper.BBANDS(timeperiod=20, nbdevup=2.0, nbdevdn=2.0)
            bbands_results = {
                "upper": self._get_last_value(bbands_df["upperband"]),
                "middle": self._get_last_value(bbands_df["middleband"]),
                "lower": self._get_last_value(bbands_df["lowerband"]),
            }
        except Exception as e:
            print(f"Error calculating BBANDS: {e}")
            bbands_results = {"upper": None, "middle": None, "lower": None}

        # 4. Construir el objeto de datos con Pydantic para validación automática
        try:
            market_snapshot = MarketIndicators(
                trading_pair=symbol,
                overlap_studies=OverlapStudies(
                    ema=indicator_results.get("ema", None),
                    sma=indicator_results.get("sma", None),
                    bbands=BollingerBands(**bbands_results),
                    sar=indicator_results.get("sar", None),
                ),
                volatility_indicators=VolatilityIndicators(
                    atr=indicator_results.get("atr")
                ),
                momentum_indicators=MomentumIndicators(
                    rsi=indicator_results.get("rsi") or 0.0
                ),
                price_indicators=PriceIndicators(
                    avg_price=indicator_results.get("avg_price"),
                    median_price=indicator_results.get("median_price"),
                    current_price=self._get_last_value(historical_data["close"]),
                ),
                fear_and_greed=FearAndGreed(
                    value=int(fear_and_greed_data["value"])
                    if fear_and_greed_data
                    else 0,
                    value_classification=fear_and_greed_data["value_classification"]
                    if fear_and_greed_data
                    else "",
                ),
            )
            return market_snapshot
        except Exception as e:
            # Esto capturaría errores de validación de Pydantic si los datos son incorrectos
            print(f"Error creating MarketIndicators model: {e}")
            return None
