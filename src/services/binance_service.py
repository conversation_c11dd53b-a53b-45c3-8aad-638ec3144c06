import logging
import math
import os
import time

import pandas as pd
from binance.client import Client
from binance.enums import ORDER_TYPE_MARKET, SIDE_BUY, SIDE_SELL
from binance.exceptions import BinanceAPIException
from dotenv import load_dotenv

from models.bot_config import BotConfig
from utils.logger_setup import setup_logger

logging.basicConfig(level=logging.DEBUG)
load_dotenv()


class BinanceService:
    """
    Handles all communication with the Binance API.
    """

    def __init__(self, config: BotConfig):
        """Initializing the Binance Client."""
        self.config = config
        self.api_key = os.getenv("BINANCE_API_KEY_TESTNET")
        self.api_secret = os.getenv("BINANCE_SECRET_KEY_TESTNET")
        self.trading_pair = config.trading_pair
        self.logger = setup_logger()

        try:
            self.client = Client(self.api_key, self.api_secret, testnet=True)
            self.client.ping()
            self.logger.info("Initialized Binance Client and connection successful.")
        except BinanceAPIException as e:
            self.logger.error(f"Failed to connect to Binance: {e}")
            self.client = None
        except Exception as e:
            self.logger.error(f"Failed to connect to Binance: {e}")
            self.client = None

    def get_market_price(self) -> float | None:
        """Fetching the current market price."""
        try:
            if self.client is not None:
                depth = self.client.get_order_book(symbol=self.trading_pair)
                depth = depth["asks"][0][0]
                self.logger.debug(f"Fetching price for {self.trading_pair}")
                return depth
            else:
                self.logger.warning("No hay una instancia del cliente de Binance")
                return None
        except BinanceAPIException as e:
            self.logger.error(f"Error fetching current market price {e}")
            return None
        except Exception as e:
            self.logger.error(f"Error fetching current market price {e}")
            return None

    def get_historical_data(
        self,
        interval=Client.KLINE_INTERVAL_4HOUR,
        start_time=None,
        end_time=None,
        limit=1000,
    ):
        """
        Fetches historical klines and returns a pandas DataFrame with correct data types.
        Includes a retry mechanism for connection errors.
        """
        retries = 3
        for i in range(retries):
            try:
                if self.client is not None:
                    klines = self.client.get_historical_klines(
                        self.trading_pair, interval, start_time, end_time, limit
                    )
                    if not klines:
                        self.logger.warning("Historical klines returned empty list.")
                        return pd.DataFrame()

                    df = pd.DataFrame(
                        klines,
                        columns=[
                            "timestamp",
                            "open",
                            "high",
                            "low",
                            "close",
                            "volume",
                            "close_time",
                            "quote_asset_volume",
                            "number_of_trades",
                            "taker_buy_base_asset_volume",
                            "taker_buy_quote_asset_volume",
                            "ignore",
                        ],
                    )

                    cols_to_numeric = ["open", "high", "low", "close", "volume"]
                    for col in cols_to_numeric:
                        df[col] = pd.to_numeric(df[col], errors="coerce")

                    return df
            except (BinanceAPIException, ConnectionResetError) as e:
                self.logger.warning(
                    f"Error fetching historical data (attempt {i + 1}/{retries}): {e}"
                )
                if i < retries - 1:
                    time.sleep(10)  # Wait 10 seconds before retrying
                else:
                    self.logger.error(
                        "Failed to fetch historical data after multiple retries."
                    )
                    return pd.DataFrame()
            except Exception as e:
                self.logger.error(
                    f"An unexpected error occurred while fetching historical data: {e}"
                )
                return pd.DataFrame()
            finally:
                self.logger.info("Historical data fetched successfully.")
        return pd.DataFrame()

    def get_account_balance(self, asset="USDT"):
        """Fetching the account balance for a given asset."""
        if self.client is not None:
            try:
                balance = self.client.get_asset_balance(asset=asset)
                self.logger.debug(f"Fetching balance for {asset}")
                return balance
            except (BinanceAPIException, ConnectionResetError) as e:
                self.logger.warning(f"Error fetching account balance: {e}")
        else:
            self.logger.warning("No hay una instancia del cliente de Binance")

    def execute_market_order(self, side: str, quantity: float) -> dict | None:
        """executing a market order (BUY or SELL)."""
        if self.client is not None:
            try:
                # Adjust quantity to meet exchange requirements
                adjusted_quantity = self._adjust_quantity(quantity)

                # Check balance for sell orders
                if side == SIDE_SELL:
                    base_asset = self.trading_pair.replace(
                        "USDT", ""
                    )  # Extract base asset (e.g., ETH from ETHUSDT)
                    balance = self.get_account_balance(base_asset)
                    available_balance = float(balance["free"]) if balance else 0
                    if available_balance < adjusted_quantity:
                        self.logger.error(
                            f"Insufficient {base_asset} balance. Available: {available_balance:.8f}, Required: {adjusted_quantity:.8f}"
                        )
                        return None

                self.logger.info(
                    f"Executing {side} order: {adjusted_quantity} {self.trading_pair}"
                )

                order = self.client.create_order(
                    symbol=self.trading_pair,
                    side=side,
                    type=ORDER_TYPE_MARKET,
                    quantity=adjusted_quantity,
                )

                self.logger.info(
                    f"Order executed successfully. Order ID: {order.get('orderId', 'N/A')}, Status: {order.get('status', 'N/A')}"
                )
                return order

            except BinanceAPIException as e:
                self.logger.error(
                    f"Failed to execute {side} order for {quantity} {self.trading_pair}: {e}"
                )
                return None
            except ValueError as e:
                self.logger.error(f"Invalid quantity for {side} order: {e}")
                return None
            except Exception as e:
                self.logger.error(f"Unexpected error executing {side} order: {e}")
                return None
        else:
            self.logger.warning("No hay una instancia del cliente de Binance")
            return None

    def execute_market_order_quote(self, side, quote_amount):
        """Execute a market order using quote currency amount (e.g., USDT amount)."""
        try:
            # Validate minimum notional value
            if not self._validate_min_notional(quote_amount):
                return None

            # Check balance before executing buy orders
            if side == SIDE_BUY:
                balance = self.get_account_balance("USDT")
                available_balance = float(balance["free"]) if balance else 0
                if available_balance < quote_amount:
                    self.logger.error(
                        f"Insufficient USDT balance. Available: {available_balance:.2f}, Required: {quote_amount:.2f}"
                    )
                    return None

            self.logger.info(f"Executing {side} order with {quote_amount:.2f} USDT")

            order = self.client.create_order(
                symbol=self.trading_pair,
                side=side,
                type=ORDER_TYPE_MARKET,
                quoteOrderQty=quote_amount,
            )

            self.client.ws_create_order

            # Validate the order response
            if not self._validate_order_response(order, side, quote_amount):
                self.logger.error("Order response validation failed")
                return None

            self.logger.info(
                f"Quote order executed successfully. Order ID: {order.get('orderId', 'N/A')}, Status: {order.get('status', 'N/A')}"
            )
            return order

        except BinanceAPIException as e:
            self.logger.error(
                f"Failed to execute {side} quote order for {quote_amount:.2f} USDT: {e}"
            )
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error executing {side} quote order: {e}")
            return None

    def execute_market_order_websocket_test(self, side, quantity):
        """Execute a test market order."""

    def _validate_order_response(self, order, expected_side, expected_quote_amount):
        """Validate that the order response makes sense."""
        try:
            if not order:
                return False

            # Check basic fields
            status = order.get("status")
            side = order.get("side")
            executed_qty = float(order.get("executedQty", 0))
            cumulative_quote_qty = float(order.get("cummulativeQuoteQty", 0))

            # Log all the details for debugging
            self.logger.info("Order Response Validation:")
            self.logger.info(f"  Order ID: {order.get('orderId')}")
            self.logger.info(f"  Status: {status}")
            self.logger.info(f"  Side: {side}")
            self.logger.info(f"  Symbol: {order.get('symbol')}")
            self.logger.info(f"  Original Qty: {order.get('origQty', 'N/A')}")
            self.logger.info(f"  Executed Qty: {executed_qty}")
            self.logger.info(f"  Cumulative Quote Qty: {cumulative_quote_qty}")
            self.logger.info(f"  Expected Quote Amount: {expected_quote_amount}")

            # Validate status
            if status != "FILLED":
                self.logger.error(f"Order not filled. Status: {status}")
                return False

            # Validate side
            if side != expected_side:
                self.logger.error(
                    f"Order side mismatch. Expected: {expected_side}, Got: {side}"
                )
                return False

            # Validate executed quantity is reasonable
            if executed_qty <= 0:
                self.logger.error(f"Invalid executed quantity: {executed_qty}")
                return False

            # Validate cumulative quote quantity
            if cumulative_quote_qty <= 0:
                self.logger.error(
                    f"Invalid cumulative quote quantity: {cumulative_quote_qty}"
                )
                return False

            # For buy orders, check that we didn't spend way more than expected
            if expected_side == SIDE_BUY:
                if cumulative_quote_qty > expected_quote_amount * 1.05:  # 5% tolerance
                    self.logger.error(
                        f"Spent too much. Expected: {expected_quote_amount:.2f}, Actual: {cumulative_quote_qty:.2f}"
                    )
                    return False

            # Calculate and validate average price
            avg_price = cumulative_quote_qty / executed_qty
            current_market_price = float(self.get_market_price())

            self.logger.info(f"  Calculated Avg Price: {avg_price:.2f}")
            self.logger.info(f"  Current Market Price: {current_market_price:.2f}")

            # Price should be within reasonable range of market price
            price_diff_pct = (
                abs(avg_price - current_market_price) / current_market_price * 100
            )
            if price_diff_pct > 5:  # 5% tolerance
                self.logger.warning(
                    f"Price difference {price_diff_pct:.2f}% is high but within slippage tolerance"
                )

            # For small orders, quantity should make sense
            expected_qty = expected_quote_amount / current_market_price
            qty_diff_pct = abs(executed_qty - expected_qty) / expected_qty * 100

            self.logger.info(f"  Expected Qty: {expected_qty:.8f}")
            self.logger.info(f"  Quantity Diff: {qty_diff_pct:.2f}%")

            if qty_diff_pct > 10:  # 10% tolerance
                self.logger.warning(f"Quantity difference {qty_diff_pct:.2f}% is high")

            return True

        except Exception as e:
            self.logger.error(f"Error validating order response: {e}")
            return False

    def _validate_min_notional(self, quote_amount):
        """Validate that the order meets minimum notional requirements."""
        try:
            info = self.client.get_symbol_info(self.trading_pair)
            min_notional_filter = next(
                (f for f in info["filters"] if f["filterType"] == "MIN_NOTIONAL"), None
            )

            if min_notional_filter:
                min_notional = float(min_notional_filter["minNotional"])
                if quote_amount < min_notional:
                    self.logger.error(
                        f"Order amount {quote_amount} USDT is below minimum notional {min_notional} USDT"
                    )
                    return False

            return True
        except Exception as e:
            self.logger.error(f"Error validating minimum notional: {e}")
            return False

    def _adjust_quantity(self, quantity):
        """Adjusts quantity to meet exchange requirements for lot size and precision."""
        if self.client is not None:
            try:
                info = self.client.get_symbol_info(self.trading_pair)
                lot_size = next(
                    f for f in info["filters"] if f["filterType"] == "LOT_SIZE"
                )
                step_size = float(lot_size["stepSize"])
                min_qty = float(lot_size["minQty"])
                max_qty = float(lot_size["maxQty"])

                self.logger.debug(
                    f"Symbol {self.trading_pair} - Step size: {step_size}, Min qty: {min_qty}, Max qty: {max_qty}"
                )

                # Check if quantity is below minimum
                if quantity < min_qty:
                    raise ValueError(
                        f"Quantity {quantity} is below minimum {min_qty} for {self.trading_pair}"
                    )

                # Check if quantity is above maximum
                if quantity > max_qty:
                    raise ValueError(
                        f"Quantity {quantity} exceeds maximum {max_qty} for {self.trading_pair}"
                    )

                # Adjust to allowed precision
                precision = int(round(-math.log(step_size, 10), 0))
                adjusted_quantity = math.floor(quantity * (10**precision)) / (
                    10**precision
                )

                # Final check after adjustment
                if adjusted_quantity < min_qty:
                    raise ValueError(
                        f"Adjusted quantity {adjusted_quantity} is still below minimum {min_qty}"
                    )

                self.logger.debug(
                    f"Original quantity: {quantity}, Adjusted quantity: {adjusted_quantity}"
                )
                return adjusted_quantity

            except Exception as e:
                self.logger.error(f"Error adjusting quantity: {e}")
                raise

    def get_order_status(self, order_id):
        """Get the status of a specific order by ID."""
        if self.client is not None:
            try:
                order = self.client.get_order(
                    symbol=self.trading_pair, orderId=order_id
                )
                self.logger.debug(f"Order {order_id} status: {order.get('status')}")
                return order
            except BinanceAPIException as e:
                self.logger.error(f"Failed to get order {order_id} status: {e}")
                return None
            except Exception as e:
                self.logger.error(
                    f"Unexpected error getting order {order_id} status: {e}"
                )
                return None

    def verify_position_exists(self, base_asset):
        """Verify if we actually have the expected position in our account."""
        try:
            balance = self.get_account_balance(base_asset)
            if balance:
                free_balance = float(balance["free"])
                locked_balance = float(balance["locked"])
                total_balance = free_balance + locked_balance

                self.logger.debug(
                    f"{base_asset} balance - Free: {free_balance:.8f}, Locked: {locked_balance:.8f}, Total: {total_balance:.8f}"
                )
                return {
                    "free": free_balance,
                    "locked": locked_balance,
                    "total": total_balance,
                }
            return None
        except Exception as e:
            self.logger.error(f"Error verifying position for {base_asset}: {e}")
            return None

    def get_recent_trades(self, limit: int = 10):
        """Get recent trades for the trading pair to verify executions."""
        if self.client is not None:
            try:
                trades = self.client.get_my_trades(
                    symbol=self.trading_pair, limit=limit
                )
                self.logger.debug(f"Retrieved {len(trades)} recent trades")
                return trades
            except BinanceAPIException as e:
                self.logger.error(f"Failed to get recent trades: {e}")
                return []
            except Exception as e:
                self.logger.error(f"Unexpected error getting recent trades: {e}")
                return []

    def validate_order_execution(self, order_id, expected_side, expected_quantity=None):
        """Validate that an order was actually executed as expected."""
        try:
            order = self.get_order_status(order_id)
            if not order:
                return {"valid": False, "reason": "Order not found"}

            status = order.get("status")
            side = order.get("side")
            executed_qty = float(order.get("executedQty", 0))

            validation_result = {
                "valid": True,
                "order_id": order_id,
                "status": status,
                "side": side,
                "executed_qty": executed_qty,
                "original_qty": float(order.get("origQty", 0)),
                "cumulative_quote_qty": float(order.get("cummulativeQuoteQty", 0)),
                "avg_price": (
                    float(order.get("price", 0)) if order.get("price") else None
                ),
                "time": order.get("time"),
                "reason": "Order validated successfully",
            }

            # Check if order is actually filled
            if status != "FILLED":
                validation_result["valid"] = False
                validation_result["reason"] = f"Order status is {status}, not FILLED"
                return validation_result

            # Check if side matches
            if side != expected_side:
                validation_result["valid"] = False
                validation_result["reason"] = (
                    f"Order side {side} does not match expected {expected_side}"
                )
                return validation_result

            # Check quantity if provided
            if expected_quantity and abs(executed_qty - expected_quantity) > 0.00000001:
                validation_result["valid"] = False
                validation_result["reason"] = (
                    f"Executed quantity {executed_qty} does not match expected {expected_quantity}"
                )
                return validation_result

            return validation_result

        except Exception as e:
            self.logger.error(f"Error validating order {order_id}: {e}")
            return {"valid": False, "reason": f"Validation error: {str(e)}"}

    def get_position_summary(self):
        """Get a summary of current positions and balances."""
        try:
            base_asset = self.trading_pair.replace("USDT", "")

            usdt_balance = self.get_account_balance("USDT")
            base_balance = self.get_account_balance(base_asset)
            current_price = float(self.get_market_price())

            summary = {
                "timestamp": time.time(),
                "trading_pair": self.trading_pair,
                "current_price": current_price,
                "usdt_balance": {
                    "free": float(usdt_balance["free"]) if usdt_balance else 0,
                    "locked": float(usdt_balance["locked"]) if usdt_balance else 0,
                },
                "base_balance": {
                    "free": float(base_balance["free"]) if base_balance else 0,
                    "locked": float(base_balance["locked"]) if base_balance else 0,
                },
            }

            # Calculate total value in USDT
            total_base = (
                summary["base_balance"]["free"] + summary["base_balance"]["locked"]
            )
            total_usdt = (
                summary["usdt_balance"]["free"] + summary["usdt_balance"]["locked"]
            )
            total_value_usdt = total_usdt + (total_base * current_price)

            summary["total_value_usdt"] = total_value_usdt
            summary["base_value_usdt"] = total_base * current_price

            return summary

        except Exception as e:
            self.logger.error(f"Error getting position summary: {e}")
            return None


# def _load_config(path):
#         """Loads the YAML configuration file."""
#         try:
#             with open(path, 'r') as f:
#                 file_content = f.read()
#                 #yaml_content = file_content.split('"""')[1]
#                 return yaml.safe_load(file_content)

#         except Exception as e:
#             raise Exception(f"Failed to load config from {path}: {e}")


# config = _load_config('config.yaml')

# binace = BinanceClient(config)
# print("Market price: ")
# pprint(binace.get_market_price())
# print("Account balance: ")
# pprint(binace.get_account_balance())
# print("Executing order: ")
# quantity = round(6 / float(binace.get_market_price()), 6)
# print(f"Quantity: {quantity}")
# pprint(binace.execute_market_order_test(SIDE_BUY, quantity))
