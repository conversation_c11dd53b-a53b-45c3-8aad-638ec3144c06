from pydantic import BaseModel, Field
from typing import Optional


class BollingerBands(BaseModel):
    """Estructura validada para las Bandas de Bollinger."""

    upper: Optional[float]
    middle: Optional[float]
    lower: Optional[float]


class VolatilityIndicators(BaseModel):
    """Estructura validada para indicadores de volatilidad."""

    atr: Optional[float]


class PriceIndicators(BaseModel):
    """Estructura validada para indicadores de precio."""

    avg_price: Optional[float]
    median_price: Optional[float]
    current_price: Optional[float]


class OverlapStudies(BaseModel):
    """Estructura validada para indicadores de superposición."""

    ema: Optional[float]
    sma: Optional[float]
    bbands: BollingerBands
    sar: Optional[float]


class MomentumIndicators(BaseModel):
    """Estructura validada para indicadores de impulso."""

    rsi: float = Field(..., ge=0, le=100)  # Validación: RSI debe estar entre 0 y 100


class FearAndGreed(BaseModel):
    """Estructura validada para el índice de miedo y alegría."""

    value: int = Field(
        ..., ge=0, le=100
    )  # Validación: El valor debe estar entre 0 y 100
    value_classification: str


class MarketIndicators(BaseModel):
    """
    Un modelo de datos robusto y autovalidado para un snapshot del mercado.
    Actúa como una única fuente de verdad para el estado del mercado.
    """

    trading_pair: str
    overlap_studies: OverlapStudies
    volatility_indicators: VolatilityIndicators
    momentum_indicators: MomentumIndicators
    price_indicators: PriceIndicators
    fear_and_greed: FearAndGreed

    def is_trending_up(self) -> bool:
        """
        Al ser una clase real, podemos añadirle comportamiento (métodos).
        Esto es mucho más limpio que tener funciones de lógica sueltas.
        """
        if self.price_indicators.current_price is None or self.overlap_studies.ema is None:
            return False
        return self.price_indicators.current_price > self.overlap_studies.ema
