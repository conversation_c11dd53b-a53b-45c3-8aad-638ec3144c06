from pydantic import BaseModel, Field


class NewsSentiment(BaseModel):
    """La conclusión del Agente de Investigación."""

    sentiment: str = Field(description="Options: 'Positive', 'Negative', 'Neutral'")
    summary: str = Field(description="A brief summary of the most relevant news.")
    should_proceed: bool = Field(
        description="True if there are no critical negative news, False otherwise."
    )
