from pydantic import BaseModel


class BotConfig(BaseModel):
    """
    Define y valida toda la configuración del bot cargada desde el archivo YAML.
    """

    # Trading Parameters
    trading_pair: str

    # Risk Management & Position Sizing
    risk_per_trade_percentage: float
    atr_length: int
    atr_multiplier_for_stop_loss: float

    # EMA Settings
    ema_length: int
    ema_timeframe: str

    # DCA Strategy Settings
    initial_order_size_quote: float
    dca_deviation_percentage: float
    dca_order_size_multiplier: float
    max_dca_orders: int

    # Profit and Stop-Loss Settings
    take_profit_percentage: float
    trailing_stop_activation_percentage: float
    trailing_stop_deviation_percentage: float

    # AI Engine Settings
    ai_enabled: bool
    ai_analysis_interval_minutes: int

    # Portfolio Risk Management
    max_portfolio_allocation_percentage: float
    circuit_breaker_threshold_percentage: float

    # Kline Settings
    kline_interval: str
    kline_lookback: str

    # Operational Settings
    check_interval_seconds: int
    log_file: str
    state_file: str
