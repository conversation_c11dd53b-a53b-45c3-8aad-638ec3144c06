import os
import sys

sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))
from pprint import pprint

from services.binance_service import BinanceService
from services.ta_services import TAService
from utils.load_config import load_config

config = load_config("config/config.yaml")
binance_client = BinanceService(config)
ta_service = TAService(binance_client)
indicators = ta_service.get_indicators_value()
pprint(indicators.model_dump_json())
